# Alicres.SerialPort 高级功能详解

本文档详细介绍 Alicres.SerialPort 1.1.0 版本的 P1 级高级功能，包括高级缓冲管理、流控制和性能监控等特性。

## 📋 目录

- [高级缓冲管理](#高级缓冲管理)
- [数据流控制](#数据流控制)
- [性能监控与诊断](#性能监控与诊断)
- [高级配置选项](#高级配置选项)
- [最佳实践](#最佳实践)
- [性能优化技巧](#性能优化技巧)

---

## 🗄️ 高级缓冲管理

### AdvancedBufferManager 概述

Alicres.SerialPort 1.1.0 引入了智能缓冲区管理系统，提供多种缓冲策略和实时监控功能。

### 缓冲区溢出策略

```csharp
using Alicres.SerialPort.Models;
using Alicres.SerialPort.Services;

var config = new SerialPortConfiguration
{
    PortName = "COM1",
    BaudRate = 115200,
    ReceiveBufferSize = 8192,
    SendBufferSize = 4096,

    // 启用高级缓冲管理
    EnableAdvancedBuffering = true,
    DataQueueMaxLength = 1000,
    BufferOverflowStrategy = BufferOverflowStrategy.DropOldest
};

using var serialPort = new SerialPortService(config);

// 获取缓冲区统计信息
var bufferStats = serialPort.GetBufferStatistics();
```

### 支持的溢出策略

```csharp
public enum BufferOverflowStrategy
{
    DropOldest,     // 丢弃最旧的数据 (默认)
    DropNewest,     // 丢弃最新的数据
    Block,          // 阻塞等待空间
    ThrowException, // 抛出异常
    Expand          // 扩展缓冲区（如果内存允许）
}
```

### 缓冲区监控和事件

```csharp
// 订阅缓冲区事件
serialPort.BufferOverflow += (sender, e) =>
{
    Console.WriteLine($"缓冲区溢出: 队列已满 ({e.CurrentLength}/{e.MaxLength})");
    Console.WriteLine($"溢出数据长度: {e.OverflowData.Length} 字节");
    Console.WriteLine($"溢出时间: {e.Timestamp}");
    Console.WriteLine($"溢出数据内容: {e.OverflowData.ToText()}");
};

serialPort.BufferWarning += (sender, e) =>
{
    Console.WriteLine($"缓冲区警告: 使用率达到 {e.UsagePercentage}%");
    Console.WriteLine($"当前队列长度: {e.CurrentLength}/{e.MaxLength}");
    Console.WriteLine($"警告时间: {e.Timestamp}");

    // 当使用率超过 80% 时触发警告
    if (e.UsagePercentage > 80)
    {
        Console.WriteLine("建议增加缓冲区大小或优化数据处理速度");
    }
};
```

### 缓冲区统计信息

```csharp
// 获取缓冲区统计信息
var stats = serialPort.GetBufferStatistics();

if (stats != null)
{
    Console.WriteLine($"数据队列统计:");
    Console.WriteLine($"  队列长度: {stats.QueueLength}");
    Console.WriteLine($"  最大队列长度: {stats.MaxQueueLength}");
    Console.WriteLine($"  使用率: {stats.QueueUsagePercentage}%");
    Console.WriteLine($"  总接收字节: {stats.TotalBytesReceived}");
    Console.WriteLine($"  总丢弃字节: {stats.TotalBytesDropped}");
    Console.WriteLine($"  溢出策略: {stats.OverflowStrategy}");
    Console.WriteLine($"  最后清理时间: {stats.LastCleanupTime}");
}
else
{
    Console.WriteLine("高级缓冲管理未启用");
}

// 获取队列使用情况
var queueLength = serialPort.GetQueueLength();
var queueUsage = serialPort.GetQueueUsagePercentage();
Console.WriteLine($"当前队列长度: {queueLength}");
Console.WriteLine($"当前使用率: {queueUsage}%");
```

### 动态缓冲区管理

```csharp
// 清空数据队列
serialPort.ClearDataQueue();

// 清空系统缓冲区
serialPort.ClearReceiveBuffer();
serialPort.ClearSendBuffer();

// 批量处理数据
var batchData = serialPort.DequeueDataBatch(100); // 一次取出 100 个数据项
foreach (var data in batchData)
{
    Console.WriteLine($"处理数据: {data.ToText()} (来自 {data.PortName})");
    // 处理数据逻辑
}

// 获取缓冲区中的字节数
var bytesToRead = serialPort.GetBytesToRead();
var bytesToWrite = serialPort.GetBytesToWrite();
Console.WriteLine($"待读取字节数: {bytesToRead}");
Console.WriteLine($"待写入字节数: {bytesToWrite}");
```

---

## 🌊 数据流控制

### 流控制概述

流控制功能帮助管理数据传输速率，防止数据丢失和缓冲区溢出。

### 硬件流控制 (RTS/CTS)

```csharp
var config = new SerialPortConfiguration
{
    PortName = "COM1",
    BaudRate = 115200,
    
    // 启用硬件流控制
    EnableFlowControl = true,
    FlowControlType = FlowControlType.RtsCts
};

using var serialPort = new SerialPortService(config);

// 监控流控制状态
serialPort.FlowControlStatusChanged += (sender, e) =>
{
    Console.WriteLine($"流控制状态变化: {e.OldStatus} -> {e.NewStatus}");

    switch (e.NewStatus)
    {
        case FlowControlStatus.Normal:
            Console.WriteLine("流控制正常，数据传输正常");
            break;
        case FlowControlStatus.Paused:
            Console.WriteLine("流控制暂停，停止数据发送");
            break;
        case FlowControlStatus.Congested:
            Console.WriteLine("流控制拥塞，降低发送速率");
            break;
    }
};
```

### 软件流控制 (XON/XOFF)

```csharp
var config = new SerialPortConfiguration
{
    PortName = "COM1",
    BaudRate = 9600,
    
    // 启用软件流控制
    EnableFlowControl = true,
    FlowControlType = FlowControlType.XonXoff
};

using var serialPort = new SerialPortService(config);

// XON/XOFF 字符会自动处理
// XON (0x11) - 继续发送
// XOFF (0x13) - 暂停发送
```

### 发送速率限制

```csharp
var config = new SerialPortConfiguration
{
    PortName = "COM1",
    BaudRate = 9600,

    // 启用流控制并设置发送速率限制
    EnableFlowControl = true,
    FlowControlType = FlowControlType.XonXoff,
    SendRateLimit = 1000  // 限制为 1KB/s
};

using var serialPort = new SerialPortService(config);

// 动态调整发送速率限制
serialPort.SetSendRateLimit(2000); // 调整为 2KB/s

// 获取当前发送速率
var currentRate = serialPort.GetCurrentSendRate();
Console.WriteLine($"当前发送速率: {currentRate:F1} 字节/秒");
```

### 流控制统计

```csharp
// 获取流控制统计信息
var flowStats = serialPort.GetFlowControlStatistics();

if (flowStats != null)
{
    Console.WriteLine($"流控制统计:");
    Console.WriteLine($"  类型: {flowStats.FlowControlType}");
    Console.WriteLine($"  状态: {flowStats.CurrentStatus}");
    Console.WriteLine($"  是否启用: {flowStats.IsEnabled}");
    Console.WriteLine($"  速率限制: {flowStats.SendRateLimit} 字节/秒");
    Console.WriteLine($"  当前发送速率: {flowStats.CurrentSendRate:F1} 字节/秒");
    Console.WriteLine($"  总发送字节: {flowStats.TotalBytesSent}");
    Console.WriteLine($"  总接收字节: {flowStats.TotalBytesReceived}");
    Console.WriteLine($"  XOFF 状态: {flowStats.IsXoffReceived}");
    Console.WriteLine($"  RTS 暂停: {flowStats.IsRtsPaused}");
    Console.WriteLine($"  拥塞阈值: {flowStats.CongestionThreshold}%");
}
else
{
    Console.WriteLine("流控制未启用");
}
```

---

## 📊 性能监控与诊断

### 状态监控

```csharp
using var serialPort = new SerialPortService(config);

// 监控连接状态变化
serialPort.StatusChanged += (sender, e) =>
{
    Console.WriteLine($"=== 状态变化 ===");
    Console.WriteLine($"端口: {e.PortName}");
    Console.WriteLine($"状态变化: {e.PreviousState} -> {e.CurrentState}");
    Console.WriteLine($"变化时间: {e.Timestamp}");

    // 根据新状态执行相应操作
    switch (e.CurrentState)
    {
        case SerialPortConnectionState.Connected:
            Console.WriteLine("串口已连接，可以开始数据传输");
            break;
        case SerialPortConnectionState.Disconnected:
            Console.WriteLine("串口已断开连接");
            break;
        case SerialPortConnectionState.Connecting:
            Console.WriteLine("正在连接串口...");
            break;
        case SerialPortConnectionState.Error:
            Console.WriteLine("串口连接出现错误");
            break;
        case SerialPortConnectionState.Reconnecting:
            Console.WriteLine("正在重新连接串口...");
            break;
    }
};

// 定期检查详细状态信息
var timer = new System.Timers.Timer(30000); // 30秒间隔
timer.Elapsed += (sender, e) =>
{
    var status = serialPort.Status;
    Console.WriteLine($"=== 定期状态检查 ===");
    Console.WriteLine($"端口: {status.PortName}");
    Console.WriteLine($"连接状态: {status.ConnectionState}");
    Console.WriteLine($"是否连接: {serialPort.IsConnected}");
    Console.WriteLine($"最后连接时间: {status.LastConnectedTime}");
    Console.WriteLine($"最后断开时间: {status.LastDisconnectedTime}");
    Console.WriteLine($"重连次数: {status.ReconnectAttempts}");
    Console.WriteLine($"接收字节数: {status.BytesReceived}");
    Console.WriteLine($"发送字节数: {status.BytesSent}");
    Console.WriteLine($"错误次数: {status.ErrorCount}");
    Console.WriteLine($"最后错误: {status.LastError}");
    Console.WriteLine($"最后错误时间: {status.LastErrorTime}");
};
timer.Start();
```

### 缓冲区性能建议

```csharp
// 获取缓冲区统计并提供优化建议
var bufferStats = serialPort.GetBufferStatistics();
if (bufferStats != null)
{
    var suggestions = bufferStats.GetPerformanceSuggestions();
    Console.WriteLine("=== 缓冲区性能建议 ===");
    foreach (var suggestion in suggestions)
    {
        Console.WriteLine($"- {suggestion}");
    }
}

// 获取流控制统计并分析性能
var flowStats = serialPort.GetFlowControlStatistics();
if (flowStats != null && flowStats.IsEnabled)
{
    Console.WriteLine("=== 流控制性能分析 ===");
    if (flowStats.CurrentSendRate > flowStats.SendRateLimit * 0.9)
    {
        Console.WriteLine("- 发送速率接近限制，考虑优化数据发送策略");
    }

    if (flowStats.IsXoffReceived)
    {
        Console.WriteLine("- 检测到 XOFF 信号，对端可能处理不及时");
    }

    if (flowStats.IsRtsPaused)
    {
        Console.WriteLine("- RTS 流控制暂停中，检查硬件连接");
    }
}
```

### 系统信息收集

```csharp
// 收集系统串口信息
var availablePorts = serialPort.GetAvailablePorts();
Console.WriteLine($"=== 系统串口信息 ===");
Console.WriteLine($"可用串口数量: {availablePorts.Length}");
foreach (var port in availablePorts)
{
    Console.WriteLine($"- {port}");
}

// 当前配置信息
var config = serialPort.Configuration;
Console.WriteLine($"=== 当前配置 ===");
Console.WriteLine($"端口: {config.PortName}");
Console.WriteLine($"波特率: {config.BaudRate}");
Console.WriteLine($"数据位: {config.DataBits}");
Console.WriteLine($"停止位: {config.StopBits}");
Console.WriteLine($"校验位: {config.Parity}");
Console.WriteLine($"接收缓冲区: {config.ReceiveBufferSize} 字节");
Console.WriteLine($"发送缓冲区: {config.SendBufferSize} 字节");
Console.WriteLine($"高级缓冲: {config.EnableAdvancedBuffering}");
Console.WriteLine($"流控制: {config.EnableFlowControl} ({config.FlowControlType})");
```

---

## ⚙️ 高级配置选项

### 完整配置示例

```csharp
var advancedConfig = new SerialPortConfiguration
{
    // 基本参数
    PortName = "COM1",
    BaudRate = 115200,
    DataBits = 8,
    StopBits = StopBits.One,
    Parity = Parity.None,
    Handshake = Handshake.None,

    // 超时设置
    ReadTimeout = 5000,
    WriteTimeout = 3000,

    // 缓冲区设置
    ReceiveBufferSize = 16384,
    SendBufferSize = 8192,

    // 高级缓冲管理
    EnableAdvancedBuffering = true,
    DataQueueMaxLength = 2000,
    BufferOverflowStrategy = BufferOverflowStrategy.DropOldest,
    BufferCleanupInterval = 30000,
    BufferWarningThreshold = 80,

    // 重连设置
    EnableAutoReconnect = true,
    ReconnectInterval = 3000,
    MaxReconnectAttempts = 5,

    // 流控制设置
    EnableFlowControl = true,
    FlowControlType = FlowControlType.RtsCts,
    SendRateLimit = 10000,

    // 硬件控制
    DtrEnable = false,
    RtsEnable = false
};

using var serialPort = new SerialPortService(advancedConfig);
```

---

## 🎯 最佳实践

### 1. 缓冲区管理最佳实践

```csharp
// 根据数据量选择合适的缓冲区大小
if (expectedDataRate > 10000) // 高数据量
{
    config.ReceiveBufferSize = 32768;
    config.SendBufferSize = 16384;
    config.EnableAdvancedBuffering = true;
    config.DataQueueMaxLength = 2000;
    config.BufferOverflowStrategy = BufferOverflowStrategy.DropOldest;
}
else // 低数据量
{
    config.ReceiveBufferSize = 4096;
    config.SendBufferSize = 2048;
    config.EnableAdvancedBuffering = false;
}

// 定期监控缓冲区使用情况
var timer = new Timer(_ =>
{
    var stats = serialPort.GetBufferStatistics();
    if (stats != null && stats.QueueUsagePercentage > 90)
    {
        Console.WriteLine($"警告：缓冲区使用率过高 ({stats.QueueUsagePercentage}%)");
        // 清理旧数据或调整处理策略
        serialPort.ClearDataQueue();
    }

    // 检查队列长度
    var queueLength = serialPort.GetQueueLength();
    if (queueLength > 1500)
    {
        Console.WriteLine($"队列长度过长: {queueLength}，建议批量处理数据");
        var batchData = serialPort.DequeueDataBatch(500);
        // 批量处理数据
    }
}, null, TimeSpan.Zero, TimeSpan.FromMinutes(5));
```

### 2. 流控制最佳实践

```csharp
// 根据设备特性选择流控制类型
if (deviceSupportsHardwareFlowControl)
{
    config.FlowControlType = FlowControlType.RtsCts;
}
else if (deviceSupportsSoftwareFlowControl)
{
    config.FlowControlType = FlowControlType.XonXoff;
}
else
{
    // 使用发送速率限制作为替代
    config.SendRateLimit = baudRate / 10; // 保守估计
}
```

### 3. 状态监控最佳实践

```csharp
// 在生产环境中启用详细的状态监控
#if !DEBUG
var statusTimer = new Timer(_ =>
{
    var status = serialPort.Status;
    var bufferStats = serialPort.GetBufferStatistics();
    var flowStats = serialPort.GetFlowControlStatistics();

    // 记录关键指标
    Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 状态监控:");
    Console.WriteLine($"  连接: {serialPort.IsConnected}");
    Console.WriteLine($"  接收: {status.BytesReceived} 字节");
    Console.WriteLine($"  发送: {status.BytesSent} 字节");
    Console.WriteLine($"  错误次数: {status.ErrorCount}");

    if (bufferStats != null)
    {
        Console.WriteLine($"  队列使用率: {bufferStats.QueueUsagePercentage}%");
    }

    if (flowStats != null && flowStats.IsEnabled)
    {
        Console.WriteLine($"  发送速率: {flowStats.CurrentSendRate:F1} 字节/秒");
    }

}, null, TimeSpan.Zero, TimeSpan.FromMinutes(10));
#endif

// 设置错误阈值告警
serialPort.ErrorOccurred += (sender, e) =>
{
    Console.WriteLine($"串口错误 [{e.ErrorLevel}]: {e.Exception.Message}");
    Console.WriteLine($"端口: {e.PortName}");
    Console.WriteLine($"错误时间: {e.Timestamp}");
    Console.WriteLine($"异常类型: {e.Exception.GetType().Name}");

    // 记录详细的异常信息
    if (e.Exception.InnerException != null)
    {
        Console.WriteLine($"内部异常: {e.Exception.InnerException.Message}");
    }

    // 检查错误频率
    var status = serialPort.Status;
    if (status.ErrorCount > 10)
    {
        Console.WriteLine($"警告：错误次数过多 ({status.ErrorCount})，建议检查连接");
        Console.WriteLine($"最后错误: {status.LastError}");
        Console.WriteLine($"最后错误时间: {status.LastErrorTime}");
    }
};
```

---

## ⚡ 性能优化技巧

### 1. 批量数据处理

```csharp
// 使用批量处理提高效率
serialPort.DataReceived += async (sender, e) =>
{
    // 收集数据而不是立即处理
    dataBuffer.Add(e.Data);
    
    // 当达到批量大小时统一处理
    if (dataBuffer.Count >= batchSize)
    {
        await ProcessDataBatch(dataBuffer.ToArray());
        dataBuffer.Clear();
    }
};

// 定时处理剩余数据
var batchTimer = new Timer(async _ =>
{
    if (dataBuffer.Count > 0)
    {
        await ProcessDataBatch(dataBuffer.ToArray());
        dataBuffer.Clear();
    }
}, null, TimeSpan.Zero, TimeSpan.FromMilliseconds(100));
```

### 2. 异步操作优化

```csharp
// 使用 ConfigureAwait(false) 避免死锁
await serialPort.OpenAsync().ConfigureAwait(false);

// 发送字节数组数据
byte[] data = Encoding.UTF8.GetBytes("Hello World");
await serialPort.SendAsync(data).ConfigureAwait(false);

// 或者发送文本数据
await serialPort.SendTextAsync("Hello World").ConfigureAwait(false);

// 并行处理多个操作（注意：串口通常需要顺序发送）
var dataPackets = new List<byte[]>
{
    Encoding.UTF8.GetBytes("Packet 1"),
    Encoding.UTF8.GetBytes("Packet 2"),
    Encoding.UTF8.GetBytes("Packet 3")
};

// 顺序发送（推荐）
foreach (var packet in dataPackets)
{
    await serialPort.SendAsync(packet).ConfigureAwait(false);
    await Task.Delay(10); // 小延迟确保数据完整发送
}

// 如果确实需要并行（谨慎使用）
var tasks = dataPackets.Select(packet => serialPort.SendAsync(packet));
await Task.WhenAll(tasks);
```

### 3. 内存优化

```csharp
// 重用字节数组，避免频繁分配
private readonly byte[] _reusableBuffer = new byte[4096];

// 读取数据时重用缓冲区
var bytesRead = await serialPort.ReadAsync(_reusableBuffer, 0, _reusableBuffer.Length);
if (bytesRead > 0)
{
    // 只处理实际读取的数据
    var actualData = new byte[bytesRead];
    Array.Copy(_reusableBuffer, 0, actualData, 0, bytesRead);
    ProcessData(actualData);
}

// 使用 ArrayPool 优化大数据处理
private static readonly ArrayPool<byte> _arrayPool = ArrayPool<byte>.Shared;

public async Task ProcessLargeDataAsync(int dataSize)
{
    var buffer = _arrayPool.Rent(dataSize);
    try
    {
        // 使用缓冲区
        var bytesRead = await serialPort.ReadAsync(buffer, 0, dataSize);
        if (bytesRead > 0)
        {
            // 处理数据
            ProcessData(buffer.AsSpan(0, bytesRead));
        }
    }
    finally
    {
        _arrayPool.Return(buffer);
    }
}

// 批量处理减少内存分配
private readonly List<SerialPortData> _batchBuffer = new List<SerialPortData>(100);

private void ProcessDataInBatches()
{
    var batchData = serialPort.DequeueDataBatch(100);
    _batchBuffer.AddRange(batchData);

    if (_batchBuffer.Count >= 100)
    {
        // 批量处理
        ProcessBatch(_batchBuffer);
        _batchBuffer.Clear(); // 清空但保留容量
    }
}
```

---

**下一篇**: [示例代码集合](examples/README.md) →
**上一篇**: ← [快速入门指南](getting-started.md)
